---
title: Vibe coding
description: Vibe coding
template: doc
draft: false
lastUpdated: 2025-08-26 10:45:39
---

最近看了MoneyXYZ的 [ALL in AI: 為什麼你要立即開始Vibe Coding?](https://www.youtube.com/watch?v=sgD0UKYQC6Y) 的视频,对Vibe coding有了新的理解。

我之前以为Vibe coding就是单纯的AI沟通，做出一个看的见的产品出来。直到我看了MoneyXYZ的视频，对Vibe coding有了以下理解:

- Vibe coding的结果并不一定要做一个能看到的产品出来
- 只要你借助AI解决你的实际问题，就是Vibe coding

说白了，Vibe coding只要最终结果是OK的，你认可的就行。至于是怎么实现的，你不用关心。


#### 实践
2025-08-23号,我给自己的 [Blog](https://blog.moatkon.com) 实现了以下功能：
- 相关内容推荐
- 添加统计页面
- 添加阅读进度
- 集成 Giscus 评论系统(基于 GitHub Discussions)
- 网站配色调整,调整为Moatkon主题色

都是让AI做的，自己几乎不用介入。完成的效果很惊艳，几乎都是一遍过。这就是Vibe coding。如果没有AI,加上我自己不精通前端，让我实现以上功能至少得一周,有了AI在一个小时就能全部完成。真的，很疯狂！！！ 接受AI,拥抱AI!!!
