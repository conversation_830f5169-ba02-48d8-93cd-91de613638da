---
title: 护城河
description: Build your moat
template: splash
hero:
  tagline: 构建护城河<hr />聚焦、持续
  image:
   light: ../../assets/moatkon.svg
   dark: ../../assets/moatkon.svg
  actions:
    - text: 联系我
      link: /contact
      icon: right-arrow
      variant: secondary
    # - text: App下载
    #   link: https://median.co/share/rxkroy#androidphone
    #   icon: seti:argdown
    #   variant: secondary
lastUpdated: 2025-08-23 21:14:29
---



import { CardGrid, LinkCard,Steps,Card,Icon,Badge} from '@astrojs/starlight/components';
import RecentUpdates from '../../components/RecentUpdates.astro';


<CardGrid>
	<LinkCard
		title="软件工程师"
		description="优雅且高效地处理数据"
		href="/software-engineer/readme"
	/>
	<LinkCard
		title="英语"
		description="最好的语言不是Java、C++之类的,而是英语"
		href="/english"
	/>
	<LinkCard
		title="钢琴🎹"
		description="因为我熟悉键盘,所以我选择了带按键的乐器"
		href="/music/piano/study"
	/>
	<LinkCard
		title="分享"
		description="效率工具、博主、电影🎬、视频、网站、音乐🎵"
		href="/share"
	/>
	<LinkCard
		title="AI"
		description="Artificial intelligence,智能或者说智慧,我理解的就是概率"
		href="/ai"
	/>
	{/* <LinkCard
		title="增长计划"
		description="Growth Plan"
		href="/growth"
	/> */}
</CardGrid>


<h2 class="showcase-title">
	<span class="domain-main">Moatkon's</span>
</h2>


<CardGrid>
	<LinkCard
		title="Resume"
		description="简历"
		href="https://resume.moatkon.com"
	/>

	<LinkCard
		title="Links"
		description="链接"
		href="https://links.moatkon.com"
	/>

	<LinkCard
		title="Blog"
		description="博客"
		href="https://blog.moatkon.com"
	/>

		<LinkCard
		title="Subs"
		description="订阅"
		href="https://subs.moatkon.com"
	/>


</CardGrid>


<h2 class="showcase-title">
	<span class="domain-main">Like</span>
</h2>


<Card title="“Does this help growth or not?”" icon="sun">
“这是否有助于生长？”
> 源: [What I Learned Working For Mark Zuckerberg](/english/internet/what-i-learned-working-for-mark-zuckerberg)

可以经常问问自己这段时间做的事情,是否有利于增长,至于增长的是什么?可以是余额、技能、睡眠时间等等。确保自己所做的事情是保持正向收益的

</Card>


<Card title="书里面的一句话" icon="sun">
“生存法则很简单，就是忍人所不忍，能人所不能。忍是一条线，能是一条线，两者的间距就是生存机会。”
</Card>


<h2 class="showcase-title">
	<span class="domain-main">最新更新</span>
</h2>

<RecentUpdates />
