---
title: 变更记录
description: Change Log
template: doc
lastUpdated: 2025-08-23 22:33:07
tableOfContents: false
---

import { Badge } from '@astrojs/starlight/components';

##### 2025.08.23
- 文章目录底部添加 [Ko-fi](https://ko-fi.com/moatkon) 赞助入口

##### 2025.08.16
- 添加[阅读进度指示器](https://blog.trueberryless.org/blog/starlight-progress-indicator/)

##### 2025.07.17
- [电影页面](/share/movie/p4) 更新了很多电影。同时增加了一个[计划观看的电影](/share/movie/plan-to-watch)
- starlight 升级到了 v0.35.0


##### 2025.06.27
- 集成 astro-mermaid


##### 2025.06.20 🎉🎉🎉
- 整理项目,开源 [moatkon](https://github.com/moatkon/moatkon) 

##### 2025.06.14
- astro 从 v5.8.1 升级到 v5.9.2
- @astrojs/rss 从 v4.0.11 升级到 v4.0.12

##### 2025.06.01
1. 使用css来让markdown中的图片都居中展示。<Badge text="解决方案由AugmentCode提供" variant="tip" />

##### 2025.05.30
1. 解决Vite构建空块警告导致的样式丢失问题。删除了冗余的show-case css样式文件(之前为了样式不丢,手动冗余的)。<Badge text="解决方案由AugmentCode提供" variant="tip" />

##### 2025.05.24
1. 深色模式时,通过CSS给图片添加了网页主题色,不然看不清。因为我导出的图片都是背景透明的,黑色字体遇到深色模式,直接就看不到了,所以优化了一下

##### 之前的变更
无,因为是2025.05.24才计划做这个Change Log的,虽然可以从Git提交记录获取,但是我不想,浪费时间,珍惜当下
