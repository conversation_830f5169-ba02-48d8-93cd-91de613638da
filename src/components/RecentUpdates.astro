---
import { getCollection } from 'astro:content';
import { LinkCard } from '@astrojs/starlight/components';

// 获取所有文档
const allDocs = await getCollection('docs');

// 过滤非草稿文章，并按lastUpdated排序
const recentDocs = allDocs
  .filter(doc => {
    // 过滤掉草稿文章
    if (doc.data.draft === true) return false;
    
    // 过滤掉404页面和首页
    if (doc.slug === '404' || doc.slug === 'index') return false;
    
    // 确保有lastUpdated字段
    return doc.data.lastUpdated;
  })
  .sort((a, b) => {
    // 按lastUpdated降序排序（最新的在前）
    const dateA = new Date(a.data.lastUpdated);
    const dateB = new Date(b.data.lastUpdated);
    return dateB.getTime() - dateA.getTime();
  })
  .slice(0, 10); // 取前10篇

// 格式化日期
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}
---

<div class="recent-updates">
  {recentDocs.map((doc) => (
    <LinkCard
      title={doc.data.title}
      description={`${doc.data.description || ''} • 更新于 ${formatDate(doc.data.lastUpdated)}`}
      href={`/${doc.slug}`}
    />
  ))}
</div>

<style>
  .recent-updates {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
</style>
