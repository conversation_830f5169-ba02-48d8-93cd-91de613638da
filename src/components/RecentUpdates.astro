---
import { getCollection } from 'astro:content';

// 获取所有文档
const allDocs = await getCollection('docs');

// 过滤非草稿文章，并按lastUpdated排序
const recentDocs = allDocs
  .filter(doc => {
    // 过滤掉草稿文章
    if (doc.data.draft === true) return false;

    // 过滤掉404页面和首页
    if (doc.id === '404' || doc.id === 'index') return false;

    // 确保有lastUpdated字段
    return doc.data.lastUpdated;
  })
  .sort((a, b) => {
    // 按lastUpdated降序排序（最新的在前）
    const dateA = new Date(a.data.lastUpdated);
    const dateB = new Date(b.data.lastUpdated);
    return dateB.getTime() - dateA.getTime();
  })
  .slice(0, 10); // 取前10篇

// 格式化日期
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}


---

<div class="recent-updates">
  {recentDocs.map((doc) => (
    <div class="recent-item">
      <a href={`/${doc.id}/`} class="recent-link">
        <div class="recent-content">
          <h3 class="recent-title">{doc.data.title}</h3>
          <p class="recent-description">{doc.data.description || ''}</p>
          <span class="recent-date">更新于 {formatDate(doc.data.lastUpdated)}</span>
        </div>
      </a>
    </div>
  ))}
</div>

<style>
  .recent-updates {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .recent-item {
    border: 1px solid var(--sl-color-gray-5);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .recent-item:hover {
    border-color: var(--sl-color-accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .recent-link {
    display: block;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
  }

  .recent-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .recent-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--sl-color-white);
    line-height: 1.3;
  }

  .recent-description {
    margin: 0;
    font-size: 0.9rem;
    color: var(--sl-color-gray-2);
    line-height: 1.4;
  }

  .recent-date {
    font-size: 0.8rem;
    color: var(--sl-color-gray-3);
  }

  .recent-link:hover .recent-title {
    color: var(--sl-color-accent);
  }
</style>
